package dailyreportaddition

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	DailyReportAdditionDomainItf interface {
		GetByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error)
	}

	DailyReportAdditionResourceItf interface {
		getByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error)
	}
)

// GetByID retrieves daily report addition record by ID.
func (d *DailyReportAdditionDomain) GetByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error) {
	addition, err := d.resource.getByID(ctx, param)
	if err != nil {
		return DailyReportAddition{}, log.LogError(err, nil)
	}
	return addition, nil
}
