package dailyreportaddition

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getByID fetches daily report addition record by ID.
func (rsc DailyReportAdditionResource) getByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error) {
	var addition DailyReportAddition

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id = ?", param.ID).
		Where("deleted_at IS NULL").
		First(&addition).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return DailyReportAddition{}, nil
		}
		return DailyReportAddition{}, log.LogError(err, nil)
	}

	return addition, nil
}
