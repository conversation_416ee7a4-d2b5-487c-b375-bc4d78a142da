package sitereportworker

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	SiteReportWorkerDomainItf interface {
		UpdateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error
		DeleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error
	}

	SiteReportWorkerResourceItf interface {
		updateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error
		deleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error
	}
)

// UpdateWorker updates an existing site report worker record.
func (d *SiteReportWorkerDomain) UpdateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error {
	err := d.resource.updateWorkerWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// DeleteWorker deletes a site report worker record.
func (d *SiteReportWorkerDomain) DeleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error {
	err := d.resource.deleteWorkerWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}
